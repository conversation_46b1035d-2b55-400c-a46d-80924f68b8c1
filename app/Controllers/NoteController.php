<?php

namespace App\Controllers;

use App\Controllers\Controller;
use App\Models\KinoteRun;
use App\Security\TokenValidator;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Utils;


class NoteController extends Controller
{
    public function create()
    {

        $client = new Client();
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $file = $_FILES['audio_file'] ?? null;
        if (!$file || !isset($file['tmp_name']) || !$file['tmp_name']) {
            return $this->json(['error' => 'Missing file'], 400);
        }

        $tmpPath = $file['tmp_name'];
        $stream = Utils::tryFopen($tmpPath, 'r');
        if (!$file || !$tmpPath || !$stream) {
            return $this->json(['error' => 'Missing file'], 400);
        }

        $filename = filter_input(INPUT_POST, 'file_name', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? $file['name'];
        $filename = preg_replace('/[^a-zA-Z0-9_\-.]/', '_', $filename);

        $speakers = filter_input(INPUT_POST, 'speakers', FILTER_VALIDATE_INT);
        $folderId = filter_input(INPUT_POST, 'folder_id', FILTER_VALIDATE_INT);
        $language = filter_input(INPUT_POST, 'language', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $prompt = filter_input(INPUT_POST, 'prompt', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $design = filter_input(INPUT_POST, 'design', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $transcript = filter_var($_POST['transcribe'] ?? null, FILTER_VALIDATE_BOOLEAN);
        $transcriptType = filter_input(INPUT_POST, 'transcriptType', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? $filename;

        $authToken = $user->kinoteAuth;
        if (!$authToken) {
            return $this->json(['error' => 'Account Error'], 403);
        }

        $multipart = [
            [
                'name' => 'audio_file',
                'contents' => $stream,
                'filename' => $filename,
                'headers'  => [
                    'Content-Type' => mime_content_type($stream)
                ]
            ],
        ];
        $options = $user->options ?? [];
        $defaults = $options['default'] ?? [];
        $summary = $defaults['summary'] ?? 'yes';
        $transcribe = $defaults['transcribe'] ?? 'replicate';
        $model = $defaults['model'] ?? 'latest';
        $output = $defaults['output'] ?? [
            [
                'type' => 'email',
                'property' => 'transcribed',
                'subject' => 'Hier ist Ihre KI-note: ' . $name . '!',
                'body' => '<h2>Ihre Datei wurde erfolgreich bearbeitet!</h2> \n\n\n Sie finden die Datei nun in ihrem Dashboard. \n\n\n <h2>Zusammenfassung</h2>\n $summary \n\n\nBeste Grüße,\nIhre KI-note',
                'send_to' => $user->email,
                'cc' => '',
                'bcc' => ''
            ]
        ];

        $multipart[] = [
            'name' => 'summary',
            'contents' => $summary,
        ];
        $multipart[] = [
            'name' => 'transcribe',
            'contents' => $transcribe,
        ];
        $multipart[] = [
            'name' => 'model',
            'contents' => $model,
        ];
        $multipart[] = [
            'name' => 'output',
            'contents' => json_encode($output)
        ];

        if ($speakers !== null) {
            $multipart[] = [
                'name' => 'speakers',
                'contents' => $speakers,
            ];
        }
        if ($language !== null) {
            $multipart[] = [
                'name' => 'language',
                'contents' => $language,
            ];
        }
        if ($folderId !== null) {
            $multipart[] = [
                'name' => 'folder_id',
                'contents' => $folderId,
            ];
        }
        if ($name !== null) {
            $multipart[] = [
                'name' => 'name',
                'contents' => $name,
            ];
        }
        if ($prompt !== null) {
            $multipart[] = [
                'name' => 'prompt',
                'contents' => $prompt,
            ];
        }
        if ($design !== null) {
            $multipart[] = [
                'name' => 'design',
                'contents' => $design,
            ];
        }
        if ($transcript !== null) {
            $multipart[] = [
                'name' => 'use_transcript',
                'contents' => $transcript,
            ];
        }
        if ($transcript && $transcriptType !== null) {
            $multipart[] = [
                'name' => 'transcript_type',
                'contents' => $transcriptType,
            ];
        }

        try {
            $response = $client->post($_ENV['KINOTE_API_URL'] . 'e42596c9-b69c-4eb4-80a7-22e10771c22c', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $_ENV['KINOTE_BEARER_TOKEN'],
                ],
                'query' => [
                    'auth_token' => $authToken->token,
                ],
                'multipart' => $multipart,
            ]);

            $body = json_decode($response->getBody());

            return $this->json([
                'success' => true,
                'data' => $body,
            ]);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();

            $body = $response ? json_decode((string) $response->getBody()) : null;
            return $this->json([
                'success' => false,
                'status' => $body->status,
                'orig_message' => $body->orig_message ?? null,
                'message' => $body->message
            ], $response ? $response->getStatusCode() : 500);
        }
    }

    public function get()
    {
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $noteId = $_GET['id'] ?? null;
        $note = KinoteRun::where('id', $noteId)->where('user_id', $user->id)->first();

        if (!$note) {
            return $this->json(['error' => 'Not found'], 404);
        }

        return $this->json(['note' => $this->serializeNote($note)]);
    }

    public function delete()
    {
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $noteId = $_GET['id'] ?? null;
        KinoteRun::where('id', $noteId)->where('user_id', $user->id)->delete();

        return $this->json(['success' => true, 'message' => 'Note deleted']);
    }

    public function list()
    {
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        // Pagination
        $page = (int)($_GET['page'] ?? 1);
        $pageSize = (int)($_GET['page_size'] ?? 10);
        $offset = ($page - 1) * $pageSize;

        // Build query
        $query = KinoteRun::where('user_id', $user->id);

        // Filters
        if (!empty($_GET['start_date'])) {
            $query->whereDate('startedat', '>=', $_GET['start_date']);
        }
        if (!empty($_GET['end_date'])) {
            $query->whereDate('startedat', '<=', $_GET['end_date']);
        }
        if (!empty($_GET['folder_id'])) {
            $query->where('folder_id', "=", $_GET['folder_id']);
        }
        if (!empty($_GET['search_query'])) {
            $query->where('note', 'LIKE', '%' . $_GET['search_query'] . '%');
        }

        $notes = $query->orderBy('startedat', 'desc')
            ->offset($offset)
            ->limit($pageSize)
            ->get();

        return $this->json([
            'notes' => $notes->map([$this, 'serializeNote'])->toArray(),
            'pagination' => [
                'page' => $page,
                'page_size' => $pageSize,
            ]
        ]);
    }

    public function count()
    {
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $query = KinoteRun::where('user_id', $user->id);

        if (!empty($_GET['start_date'])) {
            $query->whereDate('startedat', '>=', $_GET['start_date']);
        }
        if (!empty($_GET['end_date'])) {
            $query->whereDate('startedat', '<=', $_GET['end_date']);
        }
        if (!empty($_GET['folder_id'])) {
            $query->where('folder_id', $_GET['folder_id']);
        }
        if (!empty($_GET['search_query'])) {
            $query->where('note', 'LIKE', '%' . $_GET['search_query'] . '%');
        }

        $count = $query->count();

        return $this->json(['count' => $count]);
    }

    public function moveToFolder()
    {
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $noteId = $input['note_id'] ?? null;
        $folderId = $input['folder_id'] ?? null;
        if (!$noteId || !$folderId) {
            return $this->json(['error' => 'Note ID and folder ID are required'], 400);
        }

        KinoteRun::where('id', $noteId)->where('user_id', $user->id)->update([
            'folder_id' => $folderId
        ]);

        return $this->json(['success' => true, 'message' => 'Note moved to folder']);
    }

    public function downloadPdf($noteId)
    {
        $client = new Client();
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $authToken = $user->kinoteAuth;
        if (!$authToken) {
            return $this->json(['error' => 'Account Error'], 403);
        }

        try {
            $response = $client->get($_ENV['KINOTE_API_URL'] . 'note/pdf2', [
                'headers' => ['Authorization' => 'Bearer ' . $_ENV['KINOTE_BEARER_TOKEN']],
                'query'   => ['auth_token' => $authToken->token, 'id' => $noteId],
            ]);

            if ($response->getStatusCode() !== 200) {
                return $this->json([
                    'error'  => 'Upstream error',
                    'status' => $response->getStatusCode(),
                    'success' => false,
                ], 502);
            }

            $pdfContent = (string) $response->getBody();
            $note = KinoteRun::where('id', $noteId)
                ->where('user_id', $user->id)
                ->first();

            $filename = $note && !empty($note->note)
                ? $this->sanitizeFilename($note->note) . '.pdf'
                : 'note.pdf';

            if (empty($pdfContent)) {
                $pdfContent = "%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 200 200] /Contents 4 0 R >>\nendobj\n4 0 obj\n<< /Length 0 >>\nstream\n\nendstream\nendobj\ntrailer\n<< /Root 1 0 R >>\n%%EOF";
            }

            return $this->json([
                'filename'    => $filename,
                'contentType' => 'application/pdf',
                'data'        => base64_encode($pdfContent),
                'success'     => true,
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => 'PDF download failed: ' . $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    private function sanitizeFilename(string $name): string
    {
        $name = preg_replace('/[\\\\\/\r\n\0<>:"|?*\x00-\x1F]+/', '', $name);
        return trim($name) ?: 'note';
    }


    public function downloadDocx($noteId)
    {
        $client = new Client();
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }

        $authToken = $user->kinoteAuth;
        if (!$authToken) {
            return $this->json(['error' => 'Account Error'], 403);
        }

        try {
            $response = $client->get($_ENV['KINOTE_API_URL'] . 'note/docx', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $_ENV['KINOTE_BEARER_TOKEN'],
                ],
                'query' => [
                    'auth_token' => $authToken->token,
                    'id' => $noteId,
                ],
            ]);

            if ($response->getStatusCode() !== 200) {
                return $this->json([
                    'error'   => 'Upstream error',
                    'status'  => $response->getStatusCode(),
                    'success' => false,
                ], 502);
            }

            $docxContent = (string) $response->getBody();
            $note = KinoteRun::where('id', $noteId)
                ->where('user_id', $user->id)
                ->first();

            $filename = $note && !empty($note->note)
                ? $this->sanitizeFilename($note->note) . '.docx'
                : 'note.docx';

            if (empty($docxContent)) {
                $docxContent = ''; // empty fallback for DOCX
            }

            return $this->json([
                'filename'    => $filename,
                'contentType' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'data'        => base64_encode($docxContent),
                'success'     => true,
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error'   => 'DOCX download failed: ' . $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    public function downloadAudio($noteId)
    {
        $user = TokenValidator::getAuthenticatedUser();
        if (!$user) {
            return $this->json(['error' => 'Invalid token'], 401);
        }
        $note = KinoteRun::where('id', $noteId)
            ->where('user_id', $user->id)
            ->first();
        if (!$note) {
            return $this->json(['error' => 'Note not found'], 404);
        }
        $audioFile = $note->audio_file;
        if (empty($audioFile) || !$audioFile['path']) {
            return $this->json(['error' => 'Audio file not found'], 404);
        }

        $client = new Client();
        try {
            $response = $client->get(
                $_ENV['KINOTE_FILESYSTEM_URL']
                    . $audioFile['path']
                    . '?token=' . $_ENV['KINOTE_API_TOKEN'],
                [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $_ENV['KINOTE_BEARER_TOKEN'],
                    ],
                    'stream' => true,
                ]
            );

            $contentType = $response->getHeaderLine('Content-Type') ?: 'application/octet-stream';
            $filename = $audioFile['fileName'] ?? 'audio.m4a';

            $binaryData = '';
            $body = $response->getBody();
            while (!$body->eof()) {
                $binaryData .= base64_encode($body->read(8192));
            }

            return $this->json([
                'filename'    => $filename,
                'contentType' => $contentType,
                'data'        => $binaryData,
                'success'     => true,
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error'   => 'Audio download failed: ' . $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    public function serializeNote($note)
    {
        $progress = 100; // Default for finished notes

        if ($note->status !== 'done') {
            $diff = (time() - strtotime($note->startedat));
            $secondsEstimate = max(($note->audio_length ?? 3600) * 0.2, 180);
            $progress = min(abs(100 / $secondsEstimate * $diff) + 1, 99);
        }

        return [
            'id' => $note->id,
            'name' => $note->note,
            'folder_id' => $note->folder_id ?? null,
            'duration' => $note->audio_length,
            'status' => $note->status,
            'created_at' => $note->startedat,
            'updated_at' => $note->updatedat,
            'file_url' => null,
            'error' => $note->error,
            'progress' => (int)$progress,
        ];
    }

    /**
     * Get request input from both POST and JSON
     */
    private function getRequestInput()
    {
        $input = $_POST;

        // Also check for JSON input
        $jsonInput = json_decode(file_get_contents('php://input'), true);
        if (is_array($jsonInput)) {
            $input = array_merge($input, $jsonInput);
        }

        return $input;
    }
}
