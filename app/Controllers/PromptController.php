<?php

namespace App\Controllers;

use App\Controllers\Controller;
use App\Models\Prompts;
use App\Security\TokenValidator;

class PromptController extends Controller
{
    /**
     * List all active prompts (system + authenticated user's prompts)
     * GET /api/prompts
     */
    public function list()
    {
        try {
            $user = TokenValidator::getAuthenticatedUser();
            if (!$user) {
                return $this->json(['error' => 'Invalid token'], 401);
            }

            $prompts = Prompts::where(function ($query) use ($user) {
                $query->where(function ($q) {
                    $q->where('active', true)
                        ->where('system', true);      // active system prompts
                })
                    ->orWhere(function ($q) use ($user) {
                        $q->where('active', true)
                            ->where('user_id', $user->id); // active user prompts
                    });
            })
                ->select('id', 'name', 'system', 'user_id')
                ->orderBy('name', 'asc')
                ->get();

            return $this->json([
                'success' => true,
                'prompts' => $prompts->map(function ($prompt) {
                    return [
                        'id'        => $prompt->id,
                        'name'      => $prompt->name
                    ];
                })->toArray()
            ]);
        } catch (\Exception $e) {
            error_log('List Prompts error: ' . $e->getMessage());
            return $this->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Get single prompt
     * GET /api/prompt/{id}
     */
    public function get($id)
    {
        try {
            $user = TokenValidator::getAuthenticatedUser();
            if (!$user) {
                return $this->json(['error' => 'Invalid token'], 401);
            }

            $prompt = Prompts::where('id', $id)
                ->where(function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orWhereNull('user_id'); // Global prompts
                })
                ->first();

            if (!$prompt) {
                return $this->json(['error' => 'Prompt not found'], 404);
            }

            return $this->json([
                'success' => true,
                'prompt' => [
                    'id' => $prompt->id,
                    'name' => $prompt->name,
                    'content' => $prompt->content,
                    'is_global' => $prompt->user_id === null
                ]
            ]);
        } catch (\Exception $e) {
            error_log('Get Prompt error: ' . $e->getMessage());
            return $this->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Create new prompt
     * POST /api/prompt
     */
    public function create()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                return $this->json(['error' => 'Method not allowed'], 405);
            }

            $user = TokenValidator::getAuthenticatedUser();
            if (!$user) {
                return $this->json(['error' => 'Invalid token'], 401);
            }

            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                return $this->json(['error' => 'Invalid request'], 400);
            }

            $name = trim($input['name'] ?? '');
            $content = trim($input['content'] ?? '');

            if (empty($name) || empty($content)) {
                return $this->json(['error' => 'Name and content are required'], 400);
            }

            $prompt = Prompts::create([
                'user_id' => $user->id,
                'name' => $name,
                'content' => $content
            ]);

            return $this->json([
                'success' => true,
                'message' => 'Prompt created successfully',
                'prompt' => [
                    'id' => $prompt->id,
                    'name' => $prompt->name,
                    'content' => $prompt->content,
                    'is_global' => false
                ]
            ], 201);
        } catch (\Exception $e) {
            error_log('Create Prompt error: ' . $e->getMessage());
            return $this->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Update prompt
     * PUT /api/prompt/{id}
     */
    public function update($id)
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
                return $this->json(['error' => 'Method not allowed'], 405);
            }

            $user = TokenValidator::getAuthenticatedUser();
            if (!$user) {
                return $this->json(['error' => 'Invalid token'], 401);
            }

            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                return $this->json(['error' => 'Invalid request'], 400);
            }

            $name = trim($input['name'] ?? '');
            $content = trim($input['content'] ?? '');

            if (empty($name) || empty($content)) {
                return $this->json(['error' => 'Name and content are required'], 400);
            }

            // Only allow updating user's own prompts (not global ones)
            $prompt = Prompts::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$prompt) {
                return $this->json(['error' => 'Prompt not found or not editable'], 404);
            }

            $prompt->update([
                'name' => $name,
                'content' => $content
            ]);

            return $this->json([
                'success' => true,
                'message' => 'Prompt updated successfully',
                'prompt' => [
                    'id' => $prompt->id,
                    'name' => $prompt->name,
                    'content' => $prompt->content,
                    'is_global' => false
                ]
            ]);
        } catch (\Exception $e) {
            error_log('Update Prompt error: ' . $e->getMessage());
            return $this->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Delete prompt
     * DELETE /api/prompt/{id}
     */
    public function delete($id)
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                return $this->json(['error' => 'Method not allowed'], 405);
            }

            $user = TokenValidator::getAuthenticatedUser();
            if (!$user) {
                return $this->json(['error' => 'Invalid token'], 401);
            }

            // Only allow deleting user's own prompts (not global ones)
            $prompt = Prompts::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$prompt) {
                return $this->json(['error' => 'Prompt not found or not deletable'], 404);
            }

            $prompt->delete();

            return $this->json([
                'success' => true,
                'message' => 'Prompt deleted successfully'
            ]);
        } catch (\Exception $e) {
            error_log('Delete Prompt error: ' . $e->getMessage());
            return $this->json(['error' => 'Internal server error'], 500);
        }
    }
}
